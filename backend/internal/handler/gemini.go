package handler

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/generative-ai-go/genai"

	"interviewmaster/internal/config"
	"interviewmaster/internal/utils"
)

const (
	defaultTokenUses          = 1
	defaultTokenExpireMinutes = 30
	maxTokenExpireMinutes     = 60
	liveConnectAPIVersion     = "v1alpha"
	newSessionCreationWindow  = 1 * time.Minute
)

// GeminiHandler Gemini处理器
type GeminiHandler struct {
	genaiClient *genai.Client
}

// NewGeminiHandler 创建Gemini处理器实例
func NewGeminiHandler(ctx context.Context) (*GeminiHandler, error) {
	cfg := config.GlobalConfig
	if cfg == nil || cfg.Gemini.APIKey == "" {
		return nil, fmt.Errorf("Gemini配置或API Key未初始化")
	}

	// 由于我们使用REST API直接调用，不需要创建Gemini客户端
	// 这里设置为nil，因为我们在createGeminiEphemeralToken中直接调用REST API
	var client *genai.Client = nil

	return &GeminiHandler{
		genaiClient: client,
	}, nil
}

// CreateEphemeralTokenRequest 创建临时令牌请求
type CreateEphemeralTokenRequest struct {
	Domain        string `json:"domain" binding:"required"` // 面试领域
	PromptVersion string `json:"prompt_version"`            // 提示词版本，可选
	Uses          int    `json:"uses"`                      // 使用次数，默认1
	ExpireMinutes int    `json:"expire_minutes"`            // 过期时间（分钟），默认30
}

// CreateEphemeralTokenResponse 创建临时令牌响应
type CreateEphemeralTokenResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	Uses      int       `json:"uses"`
}

// CreateEphemeralToken 创建临时令牌
func (h *GeminiHandler) CreateEphemeralToken(c *gin.Context) {
	// 获取用户信息
	userIDValue, exists := c.Get("user_id")
	if !exists {
		utils.ErrorResponse(c, http.StatusUnauthorized, "用户未认证")
		return
	}
	userID, ok := userIDValue.(uint64)
	if !ok {
		utils.ErrorResponse(c, http.StatusInternalServerError, "用户ID格式错误")
		return
	}

	var req CreateEphemeralTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	// 设置默认值
	if req.Uses <= 0 {
		req.Uses = defaultTokenUses
	}
	if req.ExpireMinutes <= 0 {
		req.ExpireMinutes = defaultTokenExpireMinutes
	}

	// 验证参数
	if req.ExpireMinutes > maxTokenExpireMinutes {
		utils.ValidationErrorResponse(c, fmt.Sprintf("过期时间不能超过%d分钟", maxTokenExpireMinutes))
		return
	}

	// 调用真实的Gemini API
	token, err := h.createGeminiEphemeralToken(c.Request.Context(), userID, &req)
	if err != nil {
		slog.Error("Failed to create ephemeral token", "error", err, "userID", userID)
		utils.ErrorResponse(c, http.StatusInternalServerError, "创建临时令牌失败，请稍后重试")
		return
	}

	utils.SuccessResponse(c, token)
}

// AuthTokenRequest REST API请求结构
type AuthTokenRequest struct {
	Config AuthTokenConfig `json:"config"`
}

// AuthTokenConfig 认证令牌配置
type AuthTokenConfig struct {
	Uses                   int32                   `json:"uses"`
	ExpireTime             string                  `json:"expireTime"`
	NewSessionExpireTime   string                  `json:"newSessionExpireTime"`
	LiveConnectConstraints *LiveConnectConstraints `json:"liveConnectConstraints,omitempty"`
	HTTPOptions            *HTTPOptions            `json:"httpOptions,omitempty"`
}

// LiveConnectConstraints Live连接约束
type LiveConnectConstraints struct {
	UserID        *string `json:"userId,omitempty"`
	Domain        *string `json:"domain,omitempty"`
	PromptVersion *string `json:"promptVersion,omitempty"`
}

// HTTPOptions HTTP选项
type HTTPOptions struct {
	APIVersion string `json:"apiVersion"`
}

// AuthTokenResponse REST API响应结构
type AuthTokenResponse struct {
	Name       string    `json:"name"`
	Token      string    `json:"token"`
	ExpireTime time.Time `json:"expireTime"`
	Uses       int32     `json:"uses"`
}

// createGeminiEphemeralToken 调用Gemini API创建临时令牌
func (h *GeminiHandler) createGeminiEphemeralToken(ctx context.Context, userID uint64, req *CreateEphemeralTokenRequest) (*CreateEphemeralTokenResponse, error) {
	cfg := config.GlobalConfig
	if cfg == nil || cfg.Gemini.APIKey == "" {
		return nil, fmt.Errorf("Gemini配置或API Key未初始化")
	}

	// 设置令牌的过期时间和会话创建时间
	now := time.Now().UTC()
	expireTime := now.Add(time.Duration(req.ExpireMinutes) * time.Minute)
	newSessionExpireTime := now.Add(newSessionCreationWindow)

	// The user ID must be a string for the API.
	userIDStr := fmt.Sprintf("%d", userID)

	// 构建请求体
	authTokenReq := AuthTokenRequest{
		Config: AuthTokenConfig{
			Uses:                 int32(req.Uses),
			ExpireTime:           expireTime.Format(time.RFC3339),
			NewSessionExpireTime: newSessionExpireTime.Format(time.RFC3339),
			LiveConnectConstraints: &LiveConnectConstraints{
				UserID:        &userIDStr,
				Domain:        &req.Domain,
				PromptVersion: &req.PromptVersion,
			},
			HTTPOptions: &HTTPOptions{
				APIVersion: liveConnectAPIVersion,
			},
		},
	}

	// 序列化请求体
	jsonData, err := json.Marshal(authTokenReq)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 构建请求URL
	url := fmt.Sprintf("%s/v1alpha/authTokens", cfg.Gemini.BaseURL)

	// 记录请求信息用于调试
	slog.Info("Creating ephemeral token", "url", url, "userID", userID)

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("x-goog-api-key", cfg.Gemini.APIKey)

	// 发送请求，增加超时时间
	client := &http.Client{Timeout: 60 * time.Second}
	resp, err := client.Do(httpReq)
	if err != nil {
		slog.Error("HTTP request failed", "error", err, "url", url)
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var authTokenResp AuthTokenResponse
	if err := json.Unmarshal(body, &authTokenResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 返回结果
	return &CreateEphemeralTokenResponse{
		Token:     authTokenResp.Token,
		ExpiresAt: authTokenResp.ExpireTime,
		Uses:      int(authTokenResp.Uses),
	}, nil
}

// SystemInstructionRequest 获取系统指令请求
type SystemInstructionRequest struct {
	Domain        string `form:"domain" binding:"required"`
	PromptVersion string `form:"prompt_version"`
}

// GetSystemInstruction 获取系统指令
func (h *GeminiHandler) GetSystemInstruction(c *gin.Context) {
	var req SystemInstructionRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.ValidationErrorResponse(c, "参数错误: "+err.Error())
		return
	}

	instruction := h.buildSystemInstruction(req.Domain, req.PromptVersion)
	c.String(http.StatusOK, instruction)
}

// buildSystemInstruction 构建系统指令
func (h *GeminiHandler) buildSystemInstruction(domain, promptVersion string) string {
	// 基础指令
	baseInstruction := `你是一个专业的面试助手，专门帮助面试者回答技术面试问题。

请遵循以下原则：
1. 提供准确、专业的技术回答
2. 回答要简洁明了，重点突出
3. 适当举例说明复杂概念
4. 保持自信和专业的语调
5. 如果不确定答案，诚实说明并提供相关思路

请用中文回答所有问题。`

	// 根据领域添加专业指令
	var domainInstruction string
	switch domain {
	case "前端开发":
		domainInstruction = "\n\n你现在专注于前端开发面试，重点关注JavaScript、React、Vue、CSS、HTML5、性能优化、工程化等前端技术。"
	case "后端开发":
		domainInstruction = "\n\n你现在专注于后端开发面试，重点关注服务器架构、数据库设计、API开发、微服务、缓存、消息队列等后端技术。"
	case "算法与数据结构":
		domainInstruction = "\n\n你现在专注于算法与数据结构面试，重点关注时间复杂度、空间复杂度、常见算法思路、数据结构选择等。"
	case "系统设计":
		domainInstruction = "\n\n你现在专注于系统设计面试，重点关注高可用、高并发、分布式系统、负载均衡、数据一致性等系统架构问题。"
	default:
		domainInstruction = "\n\n你现在专注于技术面试，涵盖编程语言、框架、数据库、系统设计等各个技术领域。"
	}

	// 根据A/B测试版本调整
	var versionInstruction string
	if promptVersion == "B" {
		versionInstruction = "\n\n请在回答中更多地包含实际项目经验和最佳实践案例。"
	}

	return baseInstruction + domainInstruction + versionInstruction
}
